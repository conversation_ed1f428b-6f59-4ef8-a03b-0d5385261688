# Digital Ocean Deployment Guide

## Quick Start

### 1. Server Setup

Create a Digital Ocean droplet with:
- **OS**: Ubuntu 22.04 LTS
- **Size**: Basic plan, 2GB RAM minimum
- **Location**: Choose closest to your users
- **SSH Keys**: Add your SSH public key

### 2. Initial Server Configuration

```bash
# Connect to your server
ssh root@your-server-ip

# Run the deployment script
curl -sSL https://raw.githubusercontent.com/yourusername/os-python/main/deploy.sh | sudo bash

# Or upload and run manually:
scp deploy.sh root@your-server-ip:/tmp/
ssh root@your-server-ip
chmod +x /tmp/deploy.sh
sudo /tmp/deploy.sh
```

### 3. Upload Application Files

```bash
# From your local machine
scp -r . telegram@your-server-ip:/opt/telegram-app/

# Or use git (recommended)
ssh telegram@your-server-ip
cd /opt/telegram-app
git clone https://github.com/yourusername/os-python.git .
```

### 4. Configure Environment

```bash
# On the server as telegram user
cd /opt/telegram-app
cp .env.production.example .env.production
nano .env.production  # Add your actual credentials
chmod 600 .env.production
```

### 5. Authenticate Telegram Accounts

```bash
# Run authentication (interactive)
cd /opt/telegram-app
python telegram_auth.py
```

### 6. Start the Service

```bash
# Start the application
sudo systemctl start telegram-manager

# Check status
sudo systemctl status telegram-manager

# View logs
docker logs telegram-manager
```

## Environment Configuration

Create `/opt/telegram-app/.env.production` with your credentials:

```bash
# Telegram Account 1
TELEGRAM_ACCOUNT_1_API_ID="your_api_id"
TELEGRAM_ACCOUNT_1_API_HASH="your_api_hash"
TELEGRAM_ACCOUNT_1_PHONE="+**********"

# Add more accounts as needed...

# Target folder for new groups
TARGET_TELEGRAM_FOLDER_NAME="New Groups"

# Optional: Custom session path
TELEGRAM_SESSIONS_PATH="/app/telegram_sessions"
```

## Service Management

```bash
# Start service
sudo systemctl start telegram-manager

# Stop service
sudo systemctl stop telegram-manager

# Restart service
sudo systemctl restart telegram-manager

# Enable auto-start on boot
sudo systemctl enable telegram-manager

# View service status
sudo systemctl status telegram-manager

# View logs
journalctl -u telegram-manager -f
docker logs telegram-manager -f
```

## Backup and Restore

### Create Backup
```bash
sudo -u telegram /opt/telegram-app/backup.sh
```

### Restore from Backup
```bash
sudo -u telegram /opt/telegram-app/restore.sh
```

### Automated Backups
Add to crontab for telegram user:
```bash
sudo -u telegram crontab -e

# Add this line for daily backups at 2 AM
0 2 * * * /opt/telegram-app/backup.sh
```

## Monitoring

### Check Application Health
```bash
# Container status
docker ps

# Resource usage
docker stats telegram-manager

# Application logs
docker logs telegram-manager --tail 50

# System resources
htop
df -h
```

### Security Monitoring
```bash
# Check session file permissions
find /opt/telegram-app/sessions -type f ! -perm 600 -ls

# Monitor failed login attempts
sudo grep "Failed password" /var/log/auth.log | tail -10

# Check firewall status
sudo ufw status

# Fail2ban status
sudo fail2ban-client status sshd
```

## Troubleshooting

### Common Issues

#### Container Won't Start
```bash
# Check Docker logs
docker logs telegram-manager

# Check system resources
df -h
free -h

# Restart Docker service
sudo systemctl restart docker
```

#### Permission Errors
```bash
# Fix session permissions
sudo chown -R telegram:telegram /opt/telegram-app/sessions
sudo chmod 700 /opt/telegram-app/sessions
sudo chmod 600 /opt/telegram-app/sessions/*.session
```

#### Authentication Issues
```bash
# Re-run authentication
cd /opt/telegram-app
sudo -u telegram python telegram_auth.py

# Check environment variables
sudo -u telegram cat .env.production
```

### Log Locations
- Application logs: `/opt/telegram-app/logs/`
- Docker logs: `docker logs telegram-manager`
- System logs: `/var/log/syslog`
- Auth logs: `/var/log/auth.log`

## Security Checklist

- [ ] Firewall configured (UFW)
- [ ] Fail2ban enabled for SSH
- [ ] Non-root user created
- [ ] Session files have 600 permissions
- [ ] Environment file has 600 permissions
- [ ] Backups are encrypted
- [ ] Regular security updates enabled
- [ ] SSH key authentication only
- [ ] Strong passwords/passphrases

## Maintenance

### Regular Tasks
- **Daily**: Check application logs
- **Weekly**: System updates (`sudo apt update && sudo apt upgrade`)
- **Monthly**: Review security logs and backups
- **Quarterly**: Security audit and penetration testing

### Update Application
```bash
cd /opt/telegram-app
git pull origin main
docker-compose build
docker-compose up -d
```

## Support

For issues:
1. Check logs first
2. Review this documentation
3. Check GitHub issues
4. Contact support

Emergency contacts:
- System Admin: [Your contact]
- Security Team: [Your contact]
