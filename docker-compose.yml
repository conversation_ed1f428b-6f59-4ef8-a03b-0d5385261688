version: '3.8'

services:
  telegram-manager:
    build: .
    container_name: telegram-manager
    restart: unless-stopped
    
    # Security settings
    user: "1000:1000"  # Run as non-root user
    read_only: true    # Read-only filesystem for security
    
    # Temporary filesystems for writable areas
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
      - /var/tmp:noexec,nosuid,size=50m
    
    # Secure volume mounts
    volumes:
      # Persistent session storage with secure permissions
      - telegram_sessions:/app/telegram_sessions:rw
      # Logs volume
      - telegram_logs:/app/logs:rw
      # Read-only application code (optional, for development)
      # - .:/app:ro
    
    # Environment variables (use .env file or Docker secrets in production)
    env_file:
      - .env.production
    
    # Security options
    security_opt:
      - no-new-privileges:true
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    
    # Network security
    networks:
      - telegram-network
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a monitoring service
  # watchtower:
  #   image: containrrr/watchtower
  #   container_name: watchtower
  #   restart: unless-stopped
  #   volumes:
  #     - /var/run/docker.sock:/var/run/docker.sock
  #   command: --interval 3600 --cleanup telegram-manager

# Secure volumes with proper permissions
volumes:
  telegram_sessions:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/telegram-app/sessions
  telegram_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/telegram-app/logs

# Isolated network
networks:
  telegram-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
