# Use Python 3.12 slim image for security and size
FROM python:3.12-slim

# Set environment variables for security
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Create non-root user for security
RUN groupadd -r telegram && useradd -r -g telegram telegram

# Install system dependencies
RUN apt-get update && apt-get install -y \
    --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

# Create application directory
WORKDIR /app

# Create secure directories with proper permissions
RUN mkdir -p /app/telegram_sessions /app/logs && \
    chown -R telegram:telegram /app && \
    chmod 700 /app/telegram_sessions

# Install UV for faster Python package management
COPY --from=ghcr.io/astral-sh/uv:latest /uv /bin/uv

# Copy dependency files
COPY pyproject.toml uv.lock ./

# Install dependencies as root, then switch to non-root user
RUN uv sync --frozen --no-cache

# Copy application code
COPY --chown=telegram:telegram . .

# Switch to non-root user
USER telegram

# Set secure permissions on session directory
RUN chmod 700 /app/telegram_sessions

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)"

# Default command
CMD ["python", "telegram_manager.py"]
