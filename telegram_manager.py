import asyncio
import logging
import os
import time
from pathlib import Path
from typing import List, Dict, Optional, Any

from telethon import TelegramClient, events
from telethon.tl import types, functions
from telethon.tl.functions.messages import GetDialogFiltersRequest, UpdateDialogFilterRequest
from telethon.tl.types import Input<PERSON><PERSON><PERSON>hat, InputPeerChannel, DialogFilter
from telethon.errors import (
    FloodWaitError,
    SessionPasswordNeededError,
    PhoneCodeInvalidError,
    ApiIdInvalidError,
    FolderIdInvalidError,
)
from dotenv import load_dotenv

# Configure logging for robust console output
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


class TelegramAccountManager:
    """Manages multiple Telegram accounts and automatically moves new groups to specified folders."""

    def __init__(self):
        self.target_folder_name: Optional[str] = None
        self.accounts: List[Dict[str, Any]] = []
        self.clients: List[TelegramClient] = []

    async def load_environment(self) -> bool:
        """Load environment variables with proper error handling."""
        try:
            # Get the directory containing this script
            script_dir = Path(__file__).resolve().parent
            env_path = script_dir / ".env"

            # Load environment variables
            if env_path.exists():
                logger.info(f"Loading .env file from: {env_path}")
                load_dotenv(dotenv_path=env_path, override=True)
            else:
                logger.warning(f".env file not found at: {env_path}")
                load_dotenv()  # Try to load from system environment

            # Get target folder name
            self.target_folder_name = os.getenv("TARGET_TELEGRAM_FOLDER_NAME")
            if not self.target_folder_name:
                logger.warning(
                    "TARGET_TELEGRAM_FOLDER_NAME not set. Groups won't be moved."
                )
            else:
                logger.info(f"Target folder: '{self.target_folder_name}'")

            return True
        except Exception as e:
            logger.error(f"Error loading environment: {e}", exc_info=True)
            return False

    async def load_accounts_from_env(self) -> bool:
        """Load account configurations from environment variables."""
        try:
            self.accounts = []
            i = 1

            while True:
                api_id = os.getenv(f"TELEGRAM_ACCOUNT_{i}_API_ID")
                api_hash = os.getenv(f"TELEGRAM_ACCOUNT_{i}_API_HASH")
                phone = os.getenv(f"TELEGRAM_ACCOUNT_{i}_PHONE")

                if not (api_id and api_hash):
                    break

                # Clean and validate API ID
                try:
                    api_id_clean = str(api_id).strip('"\'')
                    api_id_int = int(api_id_clean)
                except (ValueError, TypeError) as e:
                    logger.error(
                        f"Invalid API ID for account {i}: {api_id}. Must be integer."
                    )
                    i += 1
                    continue

                # Clean other fields
                api_hash_clean = str(api_hash).strip('"\'')
                phone_clean = str(phone).strip('"\'') if phone else None

                account_config = {
                    "api_id": api_id_int,
                    "api_hash": api_hash_clean,
                    "phone": phone_clean,
                    "session_name": f"account_{i}",
                    "account_number": i,
                }

                self.accounts.append(account_config)
                logger.info(f"Loaded configuration for account {i}")
                i += 1

            if not self.accounts:
                logger.error(
                    "No valid account configurations found. "
                    "Please set TELEGRAM_ACCOUNT_1_API_ID, etc."
                )
                return False

            logger.info(f"Loaded {len(self.accounts)} account configurations")
            return True

        except Exception as e:
            logger.error(f"Error loading accounts: {e}", exc_info=True)
            return False

    async def check_account_authentication(self, account: Dict[str, Any]) -> bool:
        """Check if an account is authenticated without blocking."""
        session_file = Path(f"{account['session_name']}.session")
        if not session_file.exists():
            return False

        client = TelegramClient(
            account["session_name"],
            account["api_id"],
            account["api_hash"]
        )

        try:
            await client.connect()
            is_auth = await client.is_user_authorized()
            if is_auth:
                me = await client.get_me()
                logger.info(f"Account {account['account_number']}: {me.first_name} (authenticated)")
            return is_auth
        except Exception as e:
            logger.warning(f"Account {account['account_number']}: authentication check failed - {e}")
            return False
        finally:
            if client.is_connected():
                await client.disconnect()

    async def filter_authenticated_accounts(self) -> List[Dict[str, Any]]:
        """Filter out unauthenticated accounts and return only authenticated ones."""
        authenticated_accounts = []
        unauthenticated_accounts = []

        logger.info("Checking authentication status for all accounts...")

        for account in self.accounts:
            if await self.check_account_authentication(account):
                authenticated_accounts.append(account)
            else:
                unauthenticated_accounts.append(account)

        if unauthenticated_accounts:
            logger.warning(f"\nFound {len(unauthenticated_accounts)} unauthenticated accounts:")
            for acc in unauthenticated_accounts:
                logger.warning(f"  - Account {acc['account_number']}: {acc['phone'] or 'no phone'}")
            logger.warning("These accounts will be skipped. Run 'python telegram_auth.py' to authenticate them.")

        if authenticated_accounts:
            logger.info(f"\nUsing {len(authenticated_accounts)} authenticated accounts")
        else:
            logger.error("No authenticated accounts found! Please run 'python telegram_auth.py' first.")

        return authenticated_accounts

    async def retry_with_backoff(
        self, func, max_retries: int = 3, base_delay: float = 1.0
    ):
        """Execute function with exponential backoff retry logic."""
        for attempt in range(max_retries):
            try:
                return await func()
            except FloodWaitError as e:
                wait_time = e.seconds
                logger.warning(
                    f"Rate limited, waiting {wait_time} seconds (attempt {attempt + 1}/{max_retries})"
                )
                await asyncio.sleep(wait_time)
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                delay = base_delay * (2**attempt)
                logger.warning(
                    f"Attempt {attempt + 1} failed: {e}. Retrying in {delay} seconds..."
                )
                await asyncio.sleep(delay)

    async def get_folder_filter(self, client: TelegramClient, client_name: str) -> Optional[tuple]:
        """Get the target folder's filter object and its ID."""
        if not self.target_folder_name:
            return None

        try:
            async def _get_filters():
                return await asyncio.wait_for(
                    client(GetDialogFiltersRequest()), timeout=10.0
                )

            dialog_filters = await self.retry_with_backoff(_get_filters)

            if not hasattr(dialog_filters, "filters"):
                logger.error(
                    f"[{client_name}] Unexpected dialog_filters structure: {type(dialog_filters)}"
                )
                return None

            filters_list = dialog_filters.filters
            logger.debug(f"[{client_name}] Found {len(filters_list)} folders")

            for i, chat_folder in enumerate(filters_list):
                # Skip default folder
                if (
                    hasattr(chat_folder, "__class__")
                    and chat_folder.__class__.__name__ == "DialogFilterDefault"
                ):
                    continue

                if not hasattr(chat_folder, "title"):
                    continue

                # Extract folder title text
                folder_title = chat_folder.title
                folder_title_text = ""

                if hasattr(folder_title, "text"):
                    folder_title_text = folder_title.text
                elif isinstance(folder_title, str):
                    folder_title_text = folder_title
                else:
                    folder_title_text = str(folder_title)

                if folder_title_text == self.target_folder_name:
                    folder_id = getattr(chat_folder, "id", None)
                    if folder_id is not None:
                        logger.info(
                            f"[{client_name}] Found target folder '{self.target_folder_name}' with ID: {folder_id}"
                        )
                        return (chat_folder, int(folder_id))

            logger.warning(
                f"[{client_name}] Target folder '{self.target_folder_name}' not found"
            )
            return None

        except asyncio.TimeoutError:
            logger.error(f"[{client_name}] Timeout getting dialog filters")
            return None
        except Exception as e:
            logger.error(
                f"[{client_name}] Error getting folder filter: {e}", exc_info=True
            )
            return None

    async def get_input_peer(self, client: TelegramClient, chat_id: int):
        """Get the correct InputPeer for a chat."""
        try:
            chat_entity = await client.get_entity(chat_id)

            if isinstance(chat_entity, types.Chat):
                return InputPeerChat(chat_id=chat_entity.id)
            elif isinstance(chat_entity, types.Channel):
                return InputPeerChannel(
                    channel_id=chat_entity.id, access_hash=chat_entity.access_hash
                )
            else:
                logger.error(f"Unsupported chat type: {type(chat_entity).__name__}")
                return None
        except Exception as e:
            logger.error(f"Error getting input peer for chat {chat_id}: {e}")
            return None

    async def move_chat_to_folder(
        self, client: TelegramClient, client_name: str, chat_id: int, chat_title: str
    ) -> bool:
        """Move a chat to the target folder by updating the dialog filter."""
        try:
            folder_result = await self.get_folder_filter(client, client_name)
            if folder_result is None:
                return False

            folder_filter, folder_id = folder_result
            input_peer = await self.get_input_peer(client, chat_id)
            if input_peer is None:
                return False

            logger.info(
                f"[{client_name}] Moving chat '{chat_title}' to folder '{self.target_folder_name}'"
            )

            # Get current include_peers list
            current_include_peers = list(getattr(folder_filter, "include_peers", []))
            
            # Check if chat is already in the folder
            for peer in current_include_peers:
                if hasattr(peer, 'chat_id') and hasattr(input_peer, 'chat_id'):
                    if peer.chat_id == input_peer.chat_id:
                        logger.info(f"[{client_name}] Chat '{chat_title}' already in folder")
                        return True
                elif hasattr(peer, 'channel_id') and hasattr(input_peer, 'channel_id'):
                    if peer.channel_id == input_peer.channel_id:
                        logger.info(f"[{client_name}] Chat '{chat_title}' already in folder")
                        return True

            # Add the chat to include_peers
            current_include_peers.append(input_peer)

            # Create updated filter
            new_filter = DialogFilter(
                id=folder_id,
                title=getattr(folder_filter, "title", self.target_folder_name),
                emoticon=getattr(folder_filter, "emoticon", None),
                pinned_peers=list(getattr(folder_filter, "pinned_peers", [])),
                include_peers=current_include_peers,
                exclude_peers=list(getattr(folder_filter, "exclude_peers", [])),
                contacts=getattr(folder_filter, "contacts", False),
                non_contacts=getattr(folder_filter, "non_contacts", False),
                groups=getattr(folder_filter, "groups", True),  # Enable groups by default
                broadcasts=getattr(folder_filter, "broadcasts", False),
                bots=getattr(folder_filter, "bots", False),
                exclude_muted=getattr(folder_filter, "exclude_muted", False),
                exclude_read=getattr(folder_filter, "exclude_read", False),
                exclude_archived=getattr(folder_filter, "exclude_archived", False),
            )

            # Update the dialog filter
            async def _update_filter():
                await client(UpdateDialogFilterRequest(
                    id=folder_id,
                    filter=new_filter
                ))

            await self.retry_with_backoff(_update_filter)
            logger.info(
                f"[{client_name}] Successfully moved chat '{chat_title}' to folder"
            )
            return True

        except FolderIdInvalidError as e:
            logger.error(
                f"[{client_name}] Invalid folder ID when moving chat '{chat_title}': {e}"
            )
            return False
        except Exception as e:
            logger.error(
                f"[{client_name}] Failed to move chat '{chat_title}': {e}",
                exc_info=True,
            )
            return False

    async def handle_chat_action(
        self, event, client: TelegramClient, client_name: str
    ):
        """Handle chat action events (user joins, gets added, etc.)."""
        try:
            me = await client.get_me()
            chat = await event.get_chat()
            chat_title = getattr(chat, "title", f"Chat_{chat.id}")

            # Check if the current user is involved in the action
            user_involved = False

            if hasattr(event, "user_joined") and event.user_joined:
                if hasattr(event, "user_id") and event.user_id == me.id:
                    user_involved = True
                    logger.info(
                        f"[{client_name}] User joined group: '{chat_title}'"
                    )

            elif hasattr(event, "user_added") and event.user_added:
                # Check if current user was added
                added_users = []
                if (
                    hasattr(event, "action_message")
                    and event.action_message
                    and hasattr(event.action_message, "action")
                    and hasattr(event.action_message.action, "users")
                ):
                    added_users = event.action_message.action.users
                elif hasattr(event, "users") and event.users:
                    added_users = [
                        getattr(u, "id", None) for u in event.users if hasattr(u, "id")
                    ]

                if me.id in added_users:
                    user_involved = True
                    logger.info(
                        f"[{client_name}] User added to group: '{chat_title}'"
                    )

            elif hasattr(event, "created") and event.created:
                if hasattr(event, "user_id") and event.user_id == me.id:
                    user_involved = True
                    logger.info(
                        f"[{client_name}] User created group: '{chat_title}'"
                    )

            if user_involved and self.target_folder_name:
                # Add a small delay to ensure the chat is fully created
                await asyncio.sleep(0.5)
                await self.move_chat_to_folder(
                    client, client_name, event.chat_id, chat_title
                )

        except Exception as e:
            logger.error(
                f"[{client_name}] Error handling chat action: {e}", exc_info=True
            )

    async def run_client(self, account_details: Dict[str, Any]):
        """Initialize and run a single Telegram client."""
        api_id = account_details["api_id"]
        api_hash = account_details["api_hash"]
        phone = account_details["phone"]
        session_name = account_details["session_name"]
        account_number = account_details["account_number"]
        client_name = f"Account-{account_number}"

        client = None
        try:
            # Initialize client
            client = TelegramClient(
                session_name, api_id, api_hash, base_logger=logger
            )

            # Set up event handler
            @client.on(events.ChatAction)
            async def chat_action_handler(event):
                await self.handle_chat_action(event, client, client_name)

            # Connect (don't authenticate here - assume already authenticated)
            logger.info(f"[{client_name}] Connecting...")
            await client.connect()

            # Verify authentication (should be already authenticated)
            if not await client.is_user_authorized():
                logger.error(
                    f"[{client_name}] Account not authenticated! Run 'python telegram_auth.py' first."
                )
                return

            # Get user info
            me = await client.get_me()
            logger.info(
                f"[{client_name}] Connected as: {me.first_name} (ID: {me.id})"
            )

            # Store client for cleanup
            self.clients.append(client)

            if self.target_folder_name:
                logger.info(
                    f"[{client_name}] Listening for group joins. Target folder: '{self.target_folder_name}'"
                )
            else:
                logger.info(
                    f"[{client_name}] Listening for events (no target folder set)"
                )

            # Run until disconnected
            await client.run_until_disconnected()

        except ApiIdInvalidError:
            logger.error(f"[{client_name}] Invalid API ID or API Hash")
        except Exception as e:
            logger.error(f"[{client_name}] Client error: {e}", exc_info=True)
        finally:
            if client and client.is_connected():
                try:
                    logger.info(f"[{client_name}] Disconnecting...")
                    await client.disconnect()
                except Exception as disconnect_error:
                    logger.error(
                        f"[{client_name}] Error during disconnect: {disconnect_error}"
                    )

    async def cleanup(self):
        """Clean up all client connections."""
        logger.info("Cleaning up client connections...")
        for client in self.clients:
            try:
                if client.is_connected():
                    await client.disconnect()
            except Exception as e:
                logger.error(f"Error disconnecting client: {e}")
        self.clients.clear()

    async def run(self):
        """Main entry point to run the account manager."""
        try:
            # Load environment and accounts
            if not await self.load_environment():
                return False

            if not await self.load_accounts_from_env():
                return False

            # Filter to only authenticated accounts
            authenticated_accounts = await self.filter_authenticated_accounts()
            if not authenticated_accounts:
                logger.error("No authenticated accounts available. Run 'python telegram_auth.py' first.")
                return False

            logger.info(f"\nStarting {len(authenticated_accounts)} authenticated Telegram clients...")

            # Create tasks for authenticated accounts only
            tasks = [self.run_client(account) for account in authenticated_accounts]

            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
            else:
                logger.error("No tasks to run")
                return False

            return True

        except KeyboardInterrupt:
            logger.info("Script interrupted by user (Ctrl+C)")
            return True
        except Exception as e:
            logger.error(f"Unexpected error in main execution: {e}", exc_info=True)
            return False
        finally:
            await self.cleanup()


async def main():
    """Main function."""
    logger.info("Starting Telegram Multi-Account Manager")

    manager = TelegramAccountManager()
    success = await manager.run()

    if success:
        logger.info("Telegram Multi-Account Manager finished successfully")
    else:
        logger.error("Telegram Multi-Account Manager finished with errors")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Script interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)